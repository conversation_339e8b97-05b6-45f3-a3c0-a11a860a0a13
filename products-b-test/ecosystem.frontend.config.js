module.exports = {
  apps: [
    {
      // 前端应用配置
      name: 'products-frontend',
      script: 'serve',
      args: '-s dist -l 3001',
      cwd: '/root/products-b-test/products-b-test/product-showcase',
      instances: 1,
      exec_mode: 'fork',
      
      // 环境变量
      env: {
        NODE_ENV: 'production'
      },
      env_development: {
        NODE_ENV: 'development'
      },
      env_production: {
        NODE_ENV: 'production'
      },
      
      // 重启策略
      autorestart: true,
      watch: false,
      max_memory_restart: '500M',
      restart_delay: 2000,
      max_restarts: 5,
      min_uptime: '5s',
      
      // 日志配置
      log_file: './products-b-test/product-showcase/logs/combined.log',
      out_file: './products-b-test/product-showcase/logs/out.log',
      error_file: './products-b-test/product-showcase/logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // 其他配置
      kill_timeout: 3000,
      listen_timeout: 2000
    }
  ]
};
