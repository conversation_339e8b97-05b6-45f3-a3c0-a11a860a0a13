#!/bin/bash

# PM2部署脚本
# 用于管理前端和后端应用的PM2进程

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查PM2是否安装
check_pm2() {
    if ! command -v pm2 &> /dev/null; then
        log_error "PM2未安装，请先安装PM2: npm install -g pm2"
        exit 1
    fi
    log_info "PM2已安装，版本: $(pm2 --version)"
}

# 创建日志目录
create_log_dirs() {
    log_info "创建日志目录..."
    mkdir -p products-backend/logs
    mkdir -p product-showcase/logs
    log_success "日志目录创建完成"
}

# 构建应用
build_apps() {
    log_info "开始构建应用..."
    
    # 构建后端
    log_info "构建后端应用..."
    cd products-backend
    npm run build
    cd ..
    log_success "后端构建完成"
    
    # 构建前端
    log_info "构建前端应用..."
    cd product-showcase
    npm run build
    cd ..
    log_success "前端构建完成"
}

# 启动生产环境
start_production() {
    log_info "启动生产环境..."
    create_log_dirs
    build_apps
    
    # 停止现有进程
    pm2 delete all 2>/dev/null || true
    
    # 启动应用
    pm2 start ecosystem.config.js --env production
    
    # 保存PM2配置
    pm2 save
    
    # 设置开机自启
    pm2 startup
    
    log_success "生产环境启动完成"
    pm2 status
}

# 启动开发环境
start_development() {
    log_info "启动开发环境..."
    create_log_dirs
    
    # 停止现有进程
    pm2 delete all 2>/dev/null || true
    
    # 启动开发环境
    pm2 start ecosystem.dev.config.js
    
    log_success "开发环境启动完成"
    pm2 status
}

# 停止所有应用
stop_all() {
    log_info "停止所有应用..."
    pm2 delete all
    log_success "所有应用已停止"
}

# 重启应用
restart_apps() {
    log_info "重启应用..."
    pm2 restart all
    log_success "应用重启完成"
    pm2 status
}

# 查看日志
show_logs() {
    local app_name=${1:-all}
    log_info "显示日志: $app_name"
    pm2 logs $app_name
}

# 监控应用
monitor_apps() {
    log_info "启动PM2监控..."
    pm2 monit
}

# 显示帮助信息
show_help() {
    echo "PM2部署脚本使用说明:"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  prod, production     启动生产环境"
    echo "  dev, development     启动开发环境"
    echo "  stop                 停止所有应用"
    echo "  restart              重启所有应用"
    echo "  status               查看应用状态"
    echo "  logs [app_name]      查看日志"
    echo "  monitor              启动监控界面"
    echo "  help                 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 prod              # 启动生产环境"
    echo "  $0 dev               # 启动开发环境"
    echo "  $0 logs backend      # 查看后端日志"
    echo "  $0 monitor           # 启动监控"
}

# 主函数
main() {
    check_pm2
    
    case "${1:-help}" in
        "prod"|"production")
            start_production
            ;;
        "dev"|"development")
            start_development
            ;;
        "stop")
            stop_all
            ;;
        "restart")
            restart_apps
            ;;
        "status")
            pm2 status
            ;;
        "logs")
            show_logs $2
            ;;
        "monitor")
            monitor_apps
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
