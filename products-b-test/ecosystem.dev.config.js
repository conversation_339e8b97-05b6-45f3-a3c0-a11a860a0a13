module.exports = {
  apps: [
    {
      // 后端开发环境配置
      name: 'products-backend-dev',
      script: 'src/app.ts',
      cwd: './products-backend',
      interpreter: 'node',
      interpreter_args: '--loader ts-node/esm',
      instances: 1, // 开发环境使用单实例
      exec_mode: 'fork',
      
      // 环境变量
      env: {
        NODE_ENV: 'development',
        PORT: 3000,
        TS_NODE_PROJECT: './tsconfig.json'
      },
      
      // 重启策略
      autorestart: true,
      watch: ['src'], // 开发环境监控源码变化
      watch_delay: 1000,
      ignore_watch: ['node_modules', 'logs', 'dist', '*.log'],
      max_memory_restart: '500M',
      restart_delay: 1000,
      
      // 日志配置
      log_file: './products-backend/logs/dev-combined.log',
      out_file: './products-backend/logs/dev-out.log',
      error_file: './products-backend/logs/dev-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // 其他配置
      kill_timeout: 3000,
      listen_timeout: 2000
    },
    
    {
      // 前端开发环境配置
      name: 'products-frontend-dev',
      script: 'npm',
      args: 'run dev',
      cwd: './product-showcase',
      instances: 1,
      exec_mode: 'fork',
      
      // 环境变量
      env: {
        NODE_ENV: 'development'
      },
      
      // 重启策略
      autorestart: true,
      watch: false, // Vite有自己的热重载机制
      max_memory_restart: '300M',
      restart_delay: 2000,
      
      // 日志配置
      log_file: './product-showcase/logs/dev-combined.log',
      out_file: './product-showcase/logs/dev-out.log',
      error_file: './product-showcase/logs/dev-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // 其他配置
      kill_timeout: 3000,
      listen_timeout: 5000 // Vite启动需要更长时间
    }
  ]
};
