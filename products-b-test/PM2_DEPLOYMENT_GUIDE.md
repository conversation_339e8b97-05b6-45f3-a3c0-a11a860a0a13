# PM2部署指南

## 概述

本项目使用PM2进程管理器来管理前端和后端应用程序，确保应用的高可用性和自动重启功能。

## 项目结构

```
products-b-test/
├── ecosystem.config.js          # 生产环境PM2配置
├── ecosystem.dev.config.js      # 开发环境PM2配置
├── pm2-deploy.sh               # PM2部署脚本
├── pm2-test.sh                 # PM2配置测试脚本
├── products-backend/           # 后端应用
│   ├── src/                   # 源代码
│   ├── dist/                  # 编译后的代码
│   ├── logs/                  # 日志文件
│   └── package.json
└── product-showcase/           # 前端应用
    ├── src/                   # 源代码
    ├── dist/                  # 构建后的静态文件
    ├── logs/                  # 日志文件
    └── package.json
```

## 配置说明

### 生产环境配置 (ecosystem.config.js)

- **后端应用**: 运行2个集群实例，内存限制1GB
- **前端应用**: 使用serve静态文件服务器，内存限制500MB
- **自动重启**: 内存超限或崩溃时自动重启
- **日志管理**: 分别记录访问日志和错误日志

### 开发环境配置 (ecosystem.dev.config.js)

- **后端应用**: 使用ts-node直接运行TypeScript，支持文件监控
- **前端应用**: 使用Vite开发服务器，支持热重载
- **文件监控**: 后端源码变化时自动重启

## 快速开始

### 1. 安装依赖

确保已安装必要的全局依赖：

```bash
# 安装PM2
npm install -g pm2

# 安装serve（用于前端静态文件服务）
npm install -g serve
```

### 2. 测试配置

运行测试脚本验证配置：

```bash
./pm2-test.sh
```

### 3. 启动应用

#### 生产环境

```bash
# 使用部署脚本
./pm2-deploy.sh prod

# 或直接使用PM2命令
pm2 start ecosystem.config.js --env production
```

#### 开发环境

```bash
# 使用部署脚本
./pm2-deploy.sh dev

# 或直接使用PM2命令
pm2 start ecosystem.dev.config.js
```

## PM2常用命令

### 基本操作

```bash
# 查看所有进程状态
pm2 status
pm2 list

# 启动应用
pm2 start ecosystem.config.js

# 停止应用
pm2 stop all
pm2 stop <app_name>

# 重启应用
pm2 restart all
pm2 restart <app_name>

# 删除应用
pm2 delete all
pm2 delete <app_name>

# 重新加载应用（零停机时间）
pm2 reload all
```

### 日志管理

```bash
# 查看所有日志
pm2 logs

# 查看特定应用日志
pm2 logs products-backend
pm2 logs products-frontend

# 实时查看日志
pm2 logs --lines 100

# 清空日志
pm2 flush
```

### 监控和调试

```bash
# 启动监控界面
pm2 monit

# 查看详细信息
pm2 show <app_name>

# 查看进程列表
pm2 ps
```

### 持久化和自启动

```bash
# 保存当前进程列表
pm2 save

# 设置开机自启动
pm2 startup

# 取消开机自启动
pm2 unstartup
```

## 部署脚本使用

### pm2-deploy.sh 脚本命令

```bash
# 启动生产环境
./pm2-deploy.sh prod
./pm2-deploy.sh production

# 启动开发环境
./pm2-deploy.sh dev
./pm2-deploy.sh development

# 停止所有应用
./pm2-deploy.sh stop

# 重启所有应用
./pm2-deploy.sh restart

# 查看应用状态
./pm2-deploy.sh status

# 查看日志
./pm2-deploy.sh logs
./pm2-deploy.sh logs products-backend

# 启动监控界面
./pm2-deploy.sh monitor

# 显示帮助信息
./pm2-deploy.sh help
```

## 环境变量配置

### 后端环境变量 (.env)

主要配置项：
- `NODE_ENV`: 运行环境
- `PORT`: 服务端口
- `MONGODB_URI`: MongoDB连接字符串
- `REDIS_URL`: Redis连接字符串
- `MINIO_*`: MinIO对象存储配置
- `FEISHU_*`: 飞书API配置

### 前端环境变量 (.env)

主要配置项：
- `VITE_API_BASE_URL`: 后端API地址
- `VITE_IMAGE_BASE_URL`: 图片服务地址

## 日志管理

### 日志文件位置

- 后端日志: `products-backend/logs/`
- 前端日志: `product-showcase/logs/`

### 日志文件说明

- `combined.log`: 合并日志（包含所有输出）
- `out.log`: 标准输出日志
- `error.log`: 错误日志

### 日志轮转

建议配置logrotate来管理日志文件大小：

```bash
# 创建logrotate配置
sudo nano /etc/logrotate.d/pm2-products

# 配置内容
/root/products-b-test/products-b-test/*/logs/*.log {
    daily
    missingok
    rotate 7
    compress
    notifempty
    create 0644 root root
    postrotate
        pm2 reloadLogs
    endscript
}
```

## 性能优化建议

### 1. 实例数量调优

- **后端**: 根据CPU核心数调整集群实例数量
- **前端**: 静态文件服务通常1个实例足够

### 2. 内存限制

- 根据服务器配置调整`max_memory_restart`参数
- 监控内存使用情况，及时调整

### 3. 负载均衡

生产环境建议使用Nginx作为反向代理：

```nginx
upstream backend {
    server 127.0.0.1:3002;
}

upstream frontend {
    server 127.0.0.1:3001;
}

server {
    listen 80;
    server_name your-domain.com;

    location /api/ {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location / {
        proxy_pass http://frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 故障排除

### 常见问题

1. **应用启动失败**
   - 检查依赖是否安装完整
   - 验证环境变量配置
   - 查看错误日志

2. **端口冲突**
   - 使用`lsof -i :PORT`检查端口占用
   - 修改配置文件中的端口设置

3. **内存不足**
   - 调整`max_memory_restart`参数
   - 优化应用代码减少内存使用

4. **日志文件过大**
   - 配置日志轮转
   - 定期清理旧日志文件

### 调试命令

```bash
# 检查PM2进程
pm2 ps

# 查看详细错误信息
pm2 logs --err

# 重置PM2
pm2 kill
pm2 resurrect

# 检查系统资源
htop
free -h
df -h
```

## 安全建议

1. **权限管理**: 使用非root用户运行PM2
2. **防火墙配置**: 限制不必要的端口访问
3. **日志安全**: 避免在日志中记录敏感信息
4. **定期更新**: 保持PM2和Node.js版本更新

## 备份和恢复

### 备份PM2配置

```bash
# 导出PM2配置
pm2 save
cp ~/.pm2/dump.pm2 /backup/location/

# 备份应用配置
tar -czf app-config-backup.tar.gz ecosystem*.js .env*
```

### 恢复配置

```bash
# 恢复PM2进程
pm2 resurrect

# 或从备份恢复
cp /backup/location/dump.pm2 ~/.pm2/
pm2 resurrect
```
