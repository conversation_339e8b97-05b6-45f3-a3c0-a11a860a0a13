#!/bin/bash

# PM2配置测试脚本
# 用于验证PM2配置是否正确

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js未安装"
        return 1
    fi
    log_success "Node.js版本: $(node --version)"
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        log_error "npm未安装"
        return 1
    fi
    log_success "npm版本: $(npm --version)"
    
    # 检查PM2
    if ! command -v pm2 &> /dev/null; then
        log_error "PM2未安装"
        return 1
    fi
    log_success "PM2版本: $(pm2 --version)"
    
    # 检查serve
    if ! command -v serve &> /dev/null; then
        log_warning "serve未安装，正在安装..."
        npm install -g serve
        log_success "serve安装完成"
    else
        log_success "serve已安装"
    fi
}

# 检查项目结构
check_project_structure() {
    log_info "检查项目结构..."
    
    # 检查后端目录
    if [ ! -d "products-backend" ]; then
        log_error "后端目录不存在: products-backend"
        return 1
    fi
    log_success "后端目录存在"
    
    # 检查前端目录
    if [ ! -d "product-showcase" ]; then
        log_error "前端目录不存在: product-showcase"
        return 1
    fi
    log_success "前端目录存在"
    
    # 检查后端package.json
    if [ ! -f "products-backend/package.json" ]; then
        log_error "后端package.json不存在"
        return 1
    fi
    log_success "后端package.json存在"
    
    # 检查前端package.json
    if [ ! -f "product-showcase/package.json" ]; then
        log_error "前端package.json不存在"
        return 1
    fi
    log_success "前端package.json存在"
}

# 检查PM2配置文件
check_pm2_config() {
    log_info "检查PM2配置文件..."
    
    # 检查生产环境配置
    if [ ! -f "ecosystem.config.js" ]; then
        log_error "生产环境配置文件不存在: ecosystem.config.js"
        return 1
    fi
    log_success "生产环境配置文件存在"
    
    # 检查开发环境配置
    if [ ! -f "ecosystem.dev.config.js" ]; then
        log_error "开发环境配置文件不存在: ecosystem.dev.config.js"
        return 1
    fi
    log_success "开发环境配置文件存在"
    
    # 验证配置文件语法
    node -c ecosystem.config.js
    log_success "生产环境配置文件语法正确"
    
    node -c ecosystem.dev.config.js
    log_success "开发环境配置文件语法正确"
}

# 测试构建过程
test_build() {
    log_info "测试构建过程..."
    
    # 测试后端构建
    log_info "测试后端构建..."
    cd products-backend
    if npm run build; then
        log_success "后端构建成功"
    else
        log_error "后端构建失败"
        cd ..
        return 1
    fi
    cd ..
    
    # 测试前端构建
    log_info "测试前端构建..."
    cd product-showcase
    if npm run build; then
        log_success "前端构建成功"
    else
        log_error "前端构建失败"
        cd ..
        return 1
    fi
    cd ..
}

# 测试PM2启动
test_pm2_start() {
    log_info "测试PM2启动..."
    
    # 停止现有进程
    pm2 delete all 2>/dev/null || true
    
    # 测试开发环境启动
    log_info "测试开发环境启动..."
    if pm2 start ecosystem.dev.config.js; then
        log_success "开发环境启动成功"
        sleep 5
        pm2 status
        pm2 delete all
    else
        log_error "开发环境启动失败"
        return 1
    fi
    
    # 测试生产环境启动
    log_info "测试生产环境启动..."
    if pm2 start ecosystem.config.js --env production; then
        log_success "生产环境启动成功"
        sleep 5
        pm2 status
        pm2 delete all
    else
        log_error "生产环境启动失败"
        return 1
    fi
}

# 检查端口占用
check_ports() {
    log_info "检查端口占用..."
    
    # 检查3001端口（前端）
    if lsof -i :3001 &> /dev/null; then
        log_warning "端口3001已被占用"
        lsof -i :3001
    else
        log_success "端口3001可用"
    fi
    
    # 检查3002端口（后端）
    if lsof -i :3002 &> /dev/null; then
        log_warning "端口3002已被占用"
        lsof -i :3002
    else
        log_success "端口3002可用"
    fi
}

# 生成测试报告
generate_report() {
    log_info "生成测试报告..."
    
    cat > pm2-test-report.md << EOF
# PM2配置测试报告

## 测试时间
$(date)

## 系统信息
- 操作系统: $(uname -a)
- Node.js版本: $(node --version)
- npm版本: $(npm --version)
- PM2版本: $(pm2 --version)

## 测试结果
- [x] 依赖检查通过
- [x] 项目结构检查通过
- [x] PM2配置文件检查通过
- [x] 构建测试通过
- [x] PM2启动测试通过
- [x] 端口检查完成

## 配置文件
- 生产环境配置: ecosystem.config.js
- 开发环境配置: ecosystem.dev.config.js
- 部署脚本: pm2-deploy.sh

## 使用说明
\`\`\`bash
# 启动生产环境
./pm2-deploy.sh prod

# 启动开发环境
./pm2-deploy.sh dev

# 查看状态
./pm2-deploy.sh status

# 查看日志
./pm2-deploy.sh logs

# 停止所有应用
./pm2-deploy.sh stop
\`\`\`

## 注意事项
1. 确保MongoDB、Redis、MinIO服务正常运行
2. 检查环境变量配置是否正确
3. 定期检查日志文件大小，避免磁盘空间不足
4. 生产环境建议使用nginx作为反向代理

测试完成时间: $(date)
EOF
    
    log_success "测试报告已生成: pm2-test-report.md"
}

# 主测试函数
run_tests() {
    log_info "开始PM2配置测试..."
    
    check_dependencies || exit 1
    check_project_structure || exit 1
    check_pm2_config || exit 1
    test_build || exit 1
    check_ports
    test_pm2_start || exit 1
    generate_report
    
    log_success "所有测试通过！PM2配置正确。"
}

# 执行测试
run_tests
