#!/bin/bash

# PM2后端测试脚本
# 仅测试后端应用的PM2配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js未安装"
        return 1
    fi
    log_success "Node.js版本: $(node --version)"
    
    if ! command -v pm2 &> /dev/null; then
        log_error "PM2未安装"
        return 1
    fi
    log_success "PM2版本: $(pm2 --version)"
}

# 测试后端构建
test_backend_build() {
    log_info "测试后端构建..."
    cd products-backend
    if npm run build; then
        log_success "后端构建成功"
    else
        log_error "后端构建失败"
        cd ..
        return 1
    fi
    cd ..
}

# 创建简化的PM2配置（仅后端）
create_backend_config() {
    log_info "创建后端PM2配置..."
    
    cat > ecosystem.backend.config.js << 'EOF'
module.exports = {
  apps: [
    {
      name: 'products-backend',
      script: 'dist/app.js',
      cwd: './products-backend',
      instances: 1,
      exec_mode: 'fork',
      
      env: {
        NODE_ENV: 'production',
        PORT: 3002
      },
      
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      restart_delay: 4000,
      
      log_file: './products-backend/logs/combined.log',
      out_file: './products-backend/logs/out.log',
      error_file: './products-backend/logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      kill_timeout: 5000,
      listen_timeout: 3000
    }
  ]
};
EOF
    
    log_success "后端PM2配置创建完成"
}

# 测试PM2启动
test_pm2_backend() {
    log_info "测试后端PM2启动..."
    
    # 停止现有进程
    pm2 delete all 2>/dev/null || true
    
    # 创建日志目录
    mkdir -p products-backend/logs
    
    # 启动后端
    if pm2 start ecosystem.backend.config.js; then
        log_success "后端启动成功"
        sleep 5
        pm2 status
        
        # 检查进程是否正常运行
        if pm2 list | grep -q "online"; then
            log_success "后端进程运行正常"
        else
            log_error "后端进程状态异常"
            pm2 logs --lines 20
            return 1
        fi
        
        # 停止测试进程
        pm2 delete all
        log_success "后端测试完成"
    else
        log_error "后端启动失败"
        return 1
    fi
}

# 主测试函数
run_backend_test() {
    log_info "开始后端PM2配置测试..."
    
    check_dependencies || exit 1
    test_backend_build || exit 1
    create_backend_config
    test_pm2_backend || exit 1
    
    log_success "后端PM2配置测试通过！"
}

# 执行测试
run_backend_test
