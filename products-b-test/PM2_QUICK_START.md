# PM2快速启动指南

## 🎯 项目概述

本项目已成功配置PM2进程管理器，用于管理前端和后端应用程序。目前后端应用已完全配置并测试通过，前端应用需要修复TypeScript错误后才能使用。

## 📁 配置文件说明

### 已创建的配置文件

```
products-b-test/
├── ecosystem.config.js          # 完整的生产环境配置（前端+后端）
├── ecosystem.dev.config.js      # 开发环境配置
├── ecosystem.backend.config.js  # 仅后端配置（已测试通过）
├── pm2-deploy.sh               # 部署管理脚本
├── pm2-test.sh                 # 完整测试脚本
├── pm2-backend-test.sh         # 后端测试脚本（已通过）
└── PM2_DEPLOYMENT_GUIDE.md     # 详细部署指南
```

## 🚀 快速启动

### 1. 启动后端应用（推荐）

```bash
# 方法1：使用后端专用配置
pm2 start ecosystem.backend.config.js

# 方法2：直接启动
cd products-backend
pm2 start dist/app.js --name "products-backend"
```

### 2. 查看应用状态

```bash
# 查看所有进程状态
pm2 status
pm2 list

# 查看详细信息
pm2 show products-backend
```

### 3. 查看日志

```bash
# 查看实时日志
pm2 logs

# 查看特定应用日志
pm2 logs products-backend

# 查看最近20行日志
pm2 logs --lines 20
```

### 4. 管理应用

```bash
# 重启应用
pm2 restart products-backend

# 停止应用
pm2 stop products-backend

# 删除应用
pm2 delete products-backend

# 重新加载应用（零停机时间）
pm2 reload products-backend
```

## 🔧 配置详情

### 后端应用配置

- **应用名称**: products-backend
- **运行端口**: 3002
- **运行模式**: fork（单实例）
- **内存限制**: 1GB
- **自动重启**: 启用
- **日志路径**: `./products-backend/logs/`

### 环境变量

后端应用使用以下环境变量（来自`.env`文件）：

```bash
NODE_ENV=production
PORT=3002
MONGODB_URI=****************************************************************************************
REDIS_URL=redis://localhost:6379
# ... 其他配置
```

## 📊 监控和维护

### 1. 实时监控

```bash
# 启动监控界面
pm2 monit

# 查看进程列表
pm2 ps
```

### 2. 日志管理

```bash
# 清空日志
pm2 flush

# 重新加载日志
pm2 reloadLogs
```

### 3. 持久化配置

```bash
# 保存当前进程列表
pm2 save

# 设置开机自启动
pm2 startup

# 恢复保存的进程
pm2 resurrect
```

## 🛠️ 故障排除

### 常见问题

1. **应用启动失败**
   ```bash
   # 查看错误日志
   pm2 logs products-backend --err
   
   # 检查配置文件
   node -c ecosystem.backend.config.js
   ```

2. **端口冲突**
   ```bash
   # 检查端口占用
   lsof -i :3002
   
   # 修改端口配置
   vim products-backend/.env
   ```

3. **内存不足**
   ```bash
   # 查看内存使用
   pm2 status
   
   # 调整内存限制
   pm2 restart products-backend --max-memory-restart 2G
   ```

## 📈 性能优化建议

### 1. 生产环境配置

```bash
# 使用集群模式（多核CPU）
pm2 start ecosystem.backend.config.js -i max

# 设置合适的内存限制
pm2 restart products-backend --max-memory-restart 1G
```

### 2. 日志轮转

创建logrotate配置：

```bash
sudo nano /etc/logrotate.d/pm2-products

# 添加以下内容：
/root/products-b-test/products-b-test/products-backend/logs/*.log {
    daily
    missingok
    rotate 7
    compress
    notifempty
    create 0644 root root
    postrotate
        pm2 reloadLogs
    endscript
}
```

## 🔄 部署流程

### 标准部署流程

```bash
# 1. 停止现有应用
pm2 stop products-backend

# 2. 更新代码
git pull origin main

# 3. 安装依赖
cd products-backend && npm install

# 4. 构建应用
npm run build

# 5. 重启应用
pm2 restart products-backend

# 6. 验证状态
pm2 status
pm2 logs products-backend --lines 10
```

## 📝 下一步计划

### 前端应用配置

前端应用目前有TypeScript编译错误，需要修复后才能使用PM2管理：

1. 修复`src/utils/imageMapper.ts`中的类型错误
2. 解决其他TypeScript编译问题
3. 测试前端构建流程
4. 配置前端PM2进程

### 完整部署

修复前端问题后，可以使用完整配置：

```bash
# 使用完整配置启动前后端
pm2 start ecosystem.config.js --env production

# 使用部署脚本
./pm2-deploy.sh prod
```

## 📞 支持

如需帮助，请参考：

- 详细文档：`PM2_DEPLOYMENT_GUIDE.md`
- 测试脚本：`pm2-backend-test.sh`
- PM2官方文档：https://pm2.keymetrics.io/

---

**当前状态**: ✅ 后端PM2配置完成并测试通过  
**待完成**: 🔄 前端TypeScript错误修复
