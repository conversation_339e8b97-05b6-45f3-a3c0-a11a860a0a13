module.exports = {
  apps: [
    {
      // 后端应用配置
      name: 'products-backend',
      script: 'dist/app.js',
      cwd: './products-backend',
      instances: 2, // 运行2个实例以提高性能
      exec_mode: 'cluster',
      
      // 环境变量
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      env_development: {
        NODE_ENV: 'development',
        PORT: 3000
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      
      // 重启策略
      autorestart: true,
      watch: false, // 生产环境不建议开启文件监控
      max_memory_restart: '1G',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      
      // 日志配置
      log_file: './products-backend/logs/combined.log',
      out_file: './products-backend/logs/out.log',
      error_file: './products-backend/logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // 其他配置
      kill_timeout: 5000,
      listen_timeout: 3000,
      
      // 预启动脚本（确保构建完成）
      pre_start: 'npm run build'
    },
    
    {
      // 前端应用配置
      name: 'products-frontend',
      script: 'serve',
      args: '-s dist -l 3001',
      cwd: './product-showcase',
      instances: 1, // 静态文件服务通常1个实例足够
      exec_mode: 'fork',
      
      // 环境变量
      env: {
        NODE_ENV: 'production'
      },
      env_development: {
        NODE_ENV: 'development'
      },
      env_production: {
        NODE_ENV: 'production'
      },
      
      // 重启策略
      autorestart: true,
      watch: false,
      max_memory_restart: '500M',
      restart_delay: 2000,
      max_restarts: 5,
      min_uptime: '5s',
      
      // 日志配置
      log_file: './product-showcase/logs/combined.log',
      out_file: './product-showcase/logs/out.log',
      error_file: './product-showcase/logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // 其他配置
      kill_timeout: 3000,
      listen_timeout: 2000,
      
      // 预启动脚本（确保构建完成）
      pre_start: 'npm run build'
    }
  ],
  
  // 部署配置（可选）
  deploy: {
    production: {
      user: 'root',
      host: 'localhost',
      ref: 'origin/main',
      repo: '**************:your-repo/products-b-test.git',
      path: '/root/products-b-test',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};
